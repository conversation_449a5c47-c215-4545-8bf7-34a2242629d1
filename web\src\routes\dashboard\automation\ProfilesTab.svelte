<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Card from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import FeatureGuard from '$components/features/EnhancedFeatureGuard.svelte';
  import { getProfileData } from '$lib/utils/profile';
  import { checkAutomationEligibility } from '$lib/utils/profileHelpers';
  import { Search, Play, Plus, FileText, CheckCircle, AlertTriangle } from 'lucide-svelte';

  const { userData, profiles, onProfileSelect } = $props<{
    userData: any;
    profiles: any[];
    onProfileSelect: (profileId: string) => void;
  }>();

  // Search state
  let profileSearchQuery = $state('');

  // Filtered profiles
  const filteredProfiles = $derived(() => {
    return profiles.filter((profile) => {
      // Search filter
      if (profileSearchQuery.trim()) {
        const query = profileSearchQuery.toLowerCase();
        const profileData = getProfileData(profile);
        const name = profileData.fullName || profile.name || '';
        const title = profileData.title || '';

        return name.toLowerCase().includes(query) || title.toLowerCase().includes(query);
      }

      return true;
    });
  });
</script>

<div class="mb-6 flex flex-wrap items-center justify-between gap-4">
  <div class="flex flex-wrap items-center gap-2">
    <Button
      variant="default"
      size="sm"
      onclick={() => (window.location.href = '/dashboard/settings/profile')}
      class="gap-1">
      <Plus class="h-4 w-4" />
      Manage Profiles
    </Button>
  </div>
  <div class="relative flex items-center gap-2">
    <Search class="text-muted-foreground absolute left-2.5 top-3 h-4 w-4" />
    <Input
      placeholder="Search profiles..."
      class="h-10 w-[200px] pl-9"
      bind:value={profileSearchQuery} />
  </div>
</div>

{#if profiles.length === 0}
  <div
    class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
    <FileText class="mb-4 h-12 w-12 text-gray-400" />
    <h3 class="text-xl font-semibold text-gray-300">No profiles available</h3>
    <p class="mt-2 text-gray-400">Create a profile in Settings to start using automation</p>
    <Button
      variant="default"
      onclick={() => (window.location.href = '/dashboard/settings/profile')}
      class="mt-4">
      Go to Profile Settings
    </Button>
  </div>
{:else if filteredProfiles().length === 0}
  <div
    class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
    <Search class="mb-4 h-12 w-12 text-gray-400" />
    <h3 class="text-xl font-semibold text-gray-300">No profiles match your search</h3>
    <p class="mt-2 text-gray-400">Try adjusting your search criteria</p>
  </div>
{:else}
  <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
    {#each filteredProfiles() as profile (profile.id)}
      <Card.Root class="gap-0 p-0">
        <Card.Header class="border-border flex flex-col gap-1 border-b !p-4">
          <Card.Title>
            <a href={`/dashboard/automation/profile/${profile.id}`} class="hover:underline">
              {getProfileData(profile).fullName || 'Unnamed Profile'}
            </a>
          </Card.Title>
          <Card.Description>
            {getProfileData(profile).title || 'No title specified'}
          </Card.Description>
        </Card.Header>
        {@const eligibility = checkAutomationEligibility(profile)}
        <Progress value={eligibility.completionPercentage} max={100} class="h-2 rounded-none" />
        <div class="flex items-center justify-between px-4 py-2 text-xs">
          <span class="text-gray-500">Profile Completion</span>
          <span class="text-gray-700">{eligibility.completionPercentage}%</span>
        </div>
        <Card.Content class="p-4 pt-0">
          <!-- Profile Eligibility Status -->
          <div class="mt-2 flex flex-row items-center gap-4">
            {#if eligibility.isEligible}
              <CheckCircle class="h-4 w-4 text-green-500" />
              <span class="text-sm font-medium text-green-700">Automation Ready</span>
            {:else}
              <AlertTriangle class="h-4 w-4 text-orange-500" />
              <span class="text-sm font-medium text-orange-700">Needs Completion</span>
            {/if}
          </div>
        </Card.Content>
        <Card.Footer class="border-border border-t !p-2">
          <FeatureGuard
            {userData}
            featureId="automation"
            limitId="automation_runs_per_month"
            showUpgradePrompt={true}
            fallbackMessage="Automation features are not available in your current plan">
            {@const eligibility = checkAutomationEligibility(profile)}
            <Button
              variant="outline"
              class="flex w-full flex-row gap-2"
              disabled={!eligibility.isEligible}
              onclick={() => onProfileSelect(profile.id)}>
              <Play class="font-lighter h-2 w-2" />
              {#if eligibility.isEligible}
                Run Automation
              {:else}
                Complete Profile First
              {/if}
            </Button>
          </FeatureGuard>
        </Card.Footer>
      </Card.Root>
    {/each}
  </div>
{/if}
